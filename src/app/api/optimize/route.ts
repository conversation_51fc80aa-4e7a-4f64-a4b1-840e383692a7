import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth'
import { OptimizationEngine } from '@/lib/optimization/server-engine'
import { z } from 'zod'

// Validation schema for optimization request
const OptimizationRequestSchema = z.object({
  materials: z.array(z.object({
    id: z.string(),
    name: z.string(),
    length: z.number().positive(),
    width: z.number().positive(),
    thickness: z.number().optional(),
    unit: z.enum(['MM', 'CM', 'M', 'IN', 'FT']),
    quantity: z.number().int().positive(),
    cost: z.number().optional(),
  })),
  pieces: z.array(z.object({
    id: z.string(),
    name: z.string(),
    length: z.number().positive(),
    width: z.number().positive(),
    thickness: z.number().optional(),
    unit: z.enum(['MM', 'CM', 'M', 'IN', 'FT']),
    quantity: z.number().int().positive(),
    grainDirection: z.enum(['NONE', 'HORIZONTAL', 'VERTICAL', 'EITHER']).optional(),
    priority: z.number().int().min(1).max(10).optional(),
  })),
  sawKerf: z.number().min(0),
  kerfUnit: z.enum(['MM', 'CM', 'M', 'IN', 'FT']),
  projectId: z.string(),
})

export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const session = await auth.api.getSession({
      headers: request.headers
    })

    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validationResult = OptimizationRequestSchema.safeParse(body)

    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request data',
          details: validationResult.error.errors,
        },
        { status: 400 }
      )
    }

    const optimizationRequest = validationResult.data

    // Rate limiting check (simple implementation)
    const userAgent = request.headers.get('user-agent') || ''
    const clientIP = request.headers.get('x-forwarded-for') || 'unknown'
    
    // TODO: Implement proper rate limiting with Redis or similar
    console.log(`Optimization request from ${clientIP}, User-Agent: ${userAgent}`)

    // Initialize optimization engine
    const engine = new OptimizationEngine()

    // Perform optimization
    const startTime = Date.now()
    const result = await engine.optimize({
      ...optimizationRequest,
      userId: session.user.id,
    })

    const processingTime = Date.now() - startTime

    // TODO: Store optimization result in database if needed
    // This can be implemented later with Prisma or direct database queries

    // Return result
    return NextResponse.json({
      success: result.success,
      layouts: result.layouts,
      metadata: {
        ...result.metadata,
        processingTime,
        algorithm: 'server-optimized',
        timestamp: new Date().toISOString(),
      },
      error: result.error,
    })

  } catch (error) {
    console.error('Optimization API error:', error)
    
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error during optimization',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}

// Database storage functions can be implemented here later if needed

// Handle OPTIONS for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
